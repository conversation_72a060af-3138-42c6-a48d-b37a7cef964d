<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>龙格-库塔方法：PPT示意图（可编辑SVG）</title>
    <style>
        /* ========== CSS Custom Properties ========== */
        :root {
            /* 主色调 */
            --primary: #3b82f6;
            --primary-light: #dbeafe;
            --primary-dark: #1d4ed8;

            /* 文字颜色 */
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-muted: #9ca3af;

            /* 背景色 */
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-card: #ffffff;

            /* 边框和分割线 */
            --border-light: #e5e7eb;
            --border-medium: #d1d5db;
            --border-dark: #9ca3af;

            /* 数学图表专用色彩 */
            --curve-main: #2563eb;
            /* 主曲线 - 蓝色 */
            --euler: #dc2626;
            /* 欧拉法 - 红色 */
            --rk-k1: #ea580c;
            /* K1 - 橙红 */
            --rk-k2: #ca8a04;
            /* K2 - 黄色 */
            --rk-k3: #16a34a;
            /* K3 - 绿色 */
            --rk-k4: #7c3aed;
            /* K4 - 紫色 */
            --rk-avg: #4f46e5;
            /* 平均斜率 - 靛蓝 */

            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            /* 圆角 */
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;

            /* 间距 */
            --spacing-xs: 0.5rem;
            --spacing-sm: 0.75rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
        }

        /* ========== 基础样式重置 ========== */
        * {
            box-sizing: border-box;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Noto Sans CJK SC", "Microsoft YaHei", Roboto, Arial, sans-serif;
            line-height: 1.6;
            min-height: 100vh;
        }

        /* ========== 布局容器 ========== */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-xl) var(--spacing-md);
        }

        /* ========== 标题样式 ========== */
        .main-title {
            font-size: clamp(1.75rem, 4vw, 2.5rem);
            font-weight: 700;
            color: var(--text-primary);
            text-align: center;
            margin: 0 0 var(--spacing-2xl);
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* ========== 卡片组件 ========== */
        .card {
            background: var(--bg-card);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-light) 0%, transparent 100%);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--border-light);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 var(--spacing-xs);
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.5;
        }

        .card-content {
            padding: var(--spacing-md);
        }

        /* ========== SVG 样式 ========== */
        .svg-container {
            background: var(--bg-primary);
            border-radius: var(--radius-md);
            overflow: hidden;
        }

        svg {
            width: 100%;
            height: auto;
            display: block;
            background: var(--bg-primary);
        }

        /* ========== SVG 元素样式 ========== */
        /* 坐标轴 */
        .axis {
            stroke: var(--text-primary);
            stroke-width: 2.5;
            marker-end: url(#arrow-dark);
        }

        /* 网格线 */
        .grid {
            stroke: var(--border-light);
            stroke-width: 1;
            opacity: 0.6;
        }

        /* 主曲线 */
        .curve {
            fill: none;
            stroke: var(--curve-main);
            stroke-width: 3.5;
            filter: drop-shadow(0 2px 4px rgba(37, 99, 235, 0.2));
        }

        /* 虚线 */
        .dashed {
            stroke-dasharray: 8 4;
        }

        /* 点 */
        .pt {
            fill: var(--text-primary);
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
        }

        /* 文字标签 */
        .label {
            font-size: 16px;
            fill: var(--text-primary);
            font-weight: 500;
        }

        .label-muted {
            font-size: 14px;
            fill: var(--text-secondary);
        }

        .label-small {
            font-size: 13px;
            fill: var(--text-muted);
        }

        /* 双向箭头 */
        .h-arrow {
            stroke: var(--text-secondary);
            stroke-width: 2;
            marker-start: url(#arrow-muted-left);
            marker-end: url(#arrow-muted-right);
        }

        /* K 向量样式 */
        .k-vector {
            fill: none;
            stroke-width: 3;
            filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.2));
        }

        .k1 {
            stroke: var(--rk-k1);
        }

        .k2 {
            stroke: var(--rk-k2);
        }

        .k3 {
            stroke: var(--rk-k3);
        }

        .k4 {
            stroke: var(--rk-k4);
        }

        .avg-slope {
            stroke: var(--rk-avg);
            stroke-width: 4;
            filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.3));
        }

        /* 流程图样式 */
        .flowchart-box {
            fill: var(--bg-card);
            stroke: var(--border-medium);
            stroke-width: 2;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .flowchart-title {
            font-weight: 600;
            font-size: 15px;
        }

        .flowchart-text {
            font-size: 13px;
            fill: var(--text-secondary);
        }

        .flowchart-arrow {
            stroke: var(--text-secondary);
            stroke-width: 2.5;
            marker-end: url(#arrow-dark);
        }

        /* 徽章样式 */
        .badge {
            fill: var(--primary-light);
            stroke: var(--primary);
            stroke-width: 1;
        }

        .badge-text {
            fill: var(--primary-dark);
            font-size: 12px;
            font-weight: 500;
        }

        /* 线宽变化 */
        .stroke-thin {
            stroke-width: 2;
        }

        .stroke-thick {
            stroke-width: 4;
        }

        /* ========== 响应式设计 ========== */
        @media (max-width: 768px) {
            .container {
                padding: var(--spacing-md) var(--spacing-sm);
            }

            .card-header {
                padding: var(--spacing-md) var(--spacing-lg);
            }

            .card-title {
                font-size: 1.1rem;
            }

            .card-subtitle {
                font-size: 0.85rem;
            }

            .label {
                font-size: 14px;
            }

            .label-muted {
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: var(--spacing-sm);
            }

            .main-title {
                font-size: 1.5rem;
            }
        }

        /* ========== 打印样式 ========== */
        @media print {
            .container {
                max-width: none;
                padding: 0;
            }

            .card {
                box-shadow: none;
                border: 2px solid var(--border-medium);
                margin-bottom: var(--spacing-lg);
                page-break-inside: avoid;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="main-title">龙格-库塔方法：数值求解微分方程的可视化分析</h1>

        <!-- 通用 SVG 定义 -->
        <svg viewBox="0 0 10 10" style="height:0; position: absolute;">
            <defs>
                <marker id="arrow-dark" markerWidth="12" markerHeight="12" refX="12" refY="6" orient="auto"
                    markerUnits="strokeWidth">
                    <path d="M0,0 L12,6 L0,12 z" fill="var(--text-primary)"></path>
                </marker>
                <marker id="arrow-muted-right" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--text-secondary)"></path>
                </marker>
                <marker id="arrow-muted-left" markerWidth="10" markerHeight="10" refX="0" refY="5" orient="auto">
                    <path d="M10,0 L0,5 L10,10 z" fill="var(--text-secondary)"></path>
                </marker>
                <marker id="arrow-k1" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--rk-k1)"></path>
                </marker>
                <marker id="arrow-k2" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--rk-k2)"></path>
                </marker>
                <marker id="arrow-k3" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--rk-k3)"></path>
                </marker>
                <marker id="arrow-k4" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--rk-k4)"></path>
                </marker>
                <marker id="arrow-avg" markerWidth="10" markerHeight="10" refX="10" refY="5" orient="auto">
                    <path d="M0,0 L10,5 L0,10 z" fill="var(--rk-avg)"></path>
                </marker>
            </defs>
        </svg>

        <!-- 图1：欧拉法与平均斜率思想 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">图1 入门：欧拉法与平均斜率直观理解</h2>
                <p class="card-subtitle">
                    展示区间 [x<sub>n</sub>, x<sub>n+1</sub>] 上真实解 y(x) 与欧拉法（仅用起点斜率 K₁）之间的差异，h 为步长。平均斜率思想可显著降低误差。
                </p>
            </div>
            <div class="card-content">
                <div class="svg-container">
                    <svg viewBox="0 0 1000 600">
                        <!-- 网格线 -->
                        <g>
                            <line class="grid" x1="100" y1="120" x2="900" y2="120" />
                            <line class="grid" x1="100" y1="220" x2="900" y2="220" />
                            <line class="grid" x1="100" y1="320" x2="900" y2="320" />
                            <line class="grid" x1="100" y1="420" x2="900" y2="420" />
                        </g>

                        <!-- 坐标轴 -->
                        <line class="axis" x1="100" y1="480" x2="900" y2="480" />
                        <line class="axis" x1="100" y1="480" x2="100" y2="100" />
                        <text class="label" x="910" y="485">x</text>
                        <text class="label" x="85" y="95">y</text>

                        <!-- 真实曲线 -->
                        <path class="curve" d="M 140 410 C 280 340, 540 300, 860 160" />
                        <text class="label-muted" x="820" y="150">y(x) 真实解</text>

                        <!-- 垂直参考线 -->
                        <line class="dashed grid" x1="280" y1="100" x2="280" y2="480" />
                        <line class="dashed grid" x1="580" y1="100" x2="580" y2="480" />
                        <text class="label" x="265" y="505">x<sub>n</sub></text>
                        <text class="label" x="558" y="505">x<sub>n+1</sub></text>

                        <!-- 关键点 -->
                        <circle class="pt" cx="280" cy="345" r="5" />
                        <text class="label" x="290" y="340">y<sub>n</sub></text>
                        <circle class="pt" cx="580" cy="280" r="5" />
                        <text class="label" x="590" y="275">y(x<sub>n+1</sub>)</text>

                        <!-- 步长标注 -->
                        <line class="h-arrow" x1="280" y1="520" x2="580" y2="520" />
                        <text class="label" x="425" y="545">h</text>

                        <!-- 欧拉法切线 -->
                        <line class="k-vector k1" x1="280" y1="345" x2="580" y2="230" marker-end="url(#arrow-k1)" />
                        <text class="label" fill="var(--rk-k1)" x="430" y="235">K₁ = f(x<sub>n</sub>,
                            y<sub>n</sub>)</text>
                        <circle fill="var(--rk-k1)" cx="580" cy="230" r="5" />
                        <text class="label" fill="var(--rk-k1)" x="590" y="230">欧拉预测 ỹ<sub>n+1</sub></text>

                        <!-- 平均斜率概念 -->
                        <line class="avg-slope dashed" x1="280" y1="345" x2="580" y2="280"
                            marker-end="url(#arrow-avg)" />
                        <text class="label" fill="var(--rk-avg)" x="350" y="315">理想平均斜率 Φ</text>
                        <text class="label-small" fill="var(--rk-avg)" x="350" y="335">≈ 区间内加权平均</text>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 图2：二阶RK方法 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">图2 进阶：二阶龙格-库塔方法（中点法/Heun思想）</h2>
                <p class="card-subtitle">
                    采用预测-校正策略：先用 K₁ 预测中点值，再用中点处的斜率 K₂ 进行最终更新。与欧拉法相比，二阶RK显著减小局部截断误差。
                </p>
            </div>
            <div class="card-content">
                <div class="svg-container">
                    <svg viewBox="0 0 1000 600">
                        <!-- 坐标轴 -->
                        <line class="axis" x1="100" y1="480" x2="900" y2="480" />
                        <line class="axis" x1="100" y1="480" x2="100" y2="100" />
                        <text class="label" x="910" y="485">x</text>
                        <text class="label" x="85" y="95">y</text>

                        <!-- 真实曲线 -->
                        <path class="curve" d="M 140 410 C 280 340, 540 300, 860 160" />
                        <text class="label-muted" x="820" y="150">y(x) 真实解</text>

                        <!-- 垂直参考线 -->
                        <line class="dashed grid" x1="280" y1="100" x2="280" y2="480" />
                        <line class="dashed grid" x1="580" y1="100" x2="580" y2="480" />
                        <line class="dashed grid" x1="430" y1="100" x2="430" y2="480" />
                        <text class="label" x="265" y="505">x<sub>n</sub></text>
                        <text class="label" x="408" y="505">x<sub>n</sub> + h/2</text>
                        <text class="label" x="558" y="505">x<sub>n+1</sub></text>

                        <!-- 关键点 -->
                        <circle class="pt" cx="280" cy="345" r="5" />
                        <text class="label" x="290" y="340">y<sub>n</sub></text>
                        <circle class="pt" cx="580" cy="280" r="5" />
                        <text class="label" x="590" y="275">y(x<sub>n+1</sub>)</text>

                        <!-- K1 从起点到中点的预测 -->
                        <line class="k-vector k1" x1="280" y1="345" x2="430" y2="280" marker-end="url(#arrow-k1)" />
                        <text class="label" fill="var(--rk-k1)" x="290" y="272">K₁</text>

                        <!-- 中点预测值 -->
                        <circle fill="var(--rk-k1)" cx="430" cy="280" r="5" />
                        <text class="label" fill="var(--rk-k1)" x="440" y="275">ỹ(x<sub>n</sub>+h/2) = y<sub>n</sub> +
                            (h/2)K₁</text>

                        <!-- K2 从中点的方向 -->
                        <line class="k-vector k2" x1="430" y1="280" x2="580" y2="285" marker-end="url(#arrow-k2)" />
                        <text class="label" fill="var(--rk-k2)" x="472" y="270">K₂ = f(x<sub>n</sub> + h/2, ỹ)</text>

                        <!-- 最终更新使用K2 -->
                        <line class="k-vector k2 stroke-thick" x1="280" y1="345" x2="580" y2="285"
                            marker-end="url(#arrow-k2)" />
                        <text class="label" fill="var(--rk-k2)" x="380" y="335">y<sub>n+1</sub> = y<sub>n</sub> +
                            h·K₂（更接近真实解）</text>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 图3：四阶RK方法 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">图3 高阶：四阶龙格-库塔方法（经典RK4）</h2>
                <p class="card-subtitle">
                    通过四次函数求值获得最优精度：K₁（起点）、K₂（中点用K₁预测）、K₃（中点用K₂预测）、K₄（终点用K₃预测），
                    最终平均斜率 Φ = (K₁ + 2K₂ + 2K₃ + K₄)/6，权重比例为 1:2:2:1。
                </p>
            </div>
            <div class="card-content">
                <div class="svg-container">
                    <svg viewBox="0 0 1000 620">
                        <!-- 坐标轴 -->
                        <line class="axis" x1="100" y1="500" x2="900" y2="500" />
                        <line class="axis" x1="100" y1="500" x2="100" y2="100" />
                        <text class="label" x="910" y="505">x</text>
                        <text class="label" x="85" y="95">y</text>

                        <!-- 真实曲线 -->
                        <path class="curve" d="M 140 430 C 280 360, 540 320, 860 180" />
                        <text class="label-muted" x="820" y="170">y(x) 真实解</text>

                        <!-- 垂直参考线 -->
                        <line class="dashed grid" x1="280" y1="120" x2="280" y2="500" />
                        <line class="dashed grid" x1="580" y1="120" x2="580" y2="500" />
                        <line class="dashed grid" x1="430" y1="120" x2="430" y2="500" />
                        <text class="label" x="265" y="525">x<sub>n</sub></text>
                        <text class="label" x="408" y="525">x<sub>n</sub> + h/2</text>
                        <text class="label" x="558" y="525">x<sub>n+1</sub></text>

                        <!-- 起点和终点 -->
                        <circle class="pt" cx="280" cy="365" r="5" />
                        <text class="label" x="290" y="360">y<sub>n</sub></text>
                        <circle class="pt" cx="580" cy="300" r="5" />
                        <text class="label" x="590" y="295">y(x<sub>n+1</sub>)</text>

                        <!-- K1 -->
                        <line class="k-vector k1 stroke-thin" x1="280" y1="365" x2="380" y2="325"
                            marker-end="url(#arrow-k1)" />
                        <text class="label" fill="var(--rk-k1)" x="286" y="318">K₁</text>

                        <!-- 中点预测1 -->
                        <circle fill="var(--rk-k1)" cx="430" cy="305" r="4" />
                        <text class="label-small" fill="var(--rk-k1)" x="440" y="300">ỹ₁</text>

                        <!-- K2 -->
                        <line class="k-vector k2 stroke-thick" x1="430" y1="305" x2="490" y2="285"
                            marker-end="url(#arrow-k2)" />
                        <text class="label" fill="var(--rk-k2)" x="440" y="278">K₂</text>

                        <!-- 中点预测2 -->
                        <circle fill="var(--rk-k2)" cx="430" cy="292" r="4" />
                        <text class="label-small" fill="var(--rk-k2)" x="440" y="287">ỹ₂</text>

                        <!-- K3 -->
                        <line class="k-vector k3 stroke-thick" x1="430" y1="292" x2="490" y2="275"
                            marker-end="url(#arrow-k3)" />
                        <text class="label" fill="var(--rk-k3)" x="440" y="270">K₃</text>

                        <!-- 终点预测 -->
                        <circle fill="var(--rk-k3)" cx="580" cy="270" r="4" />
                        <text class="label-small" fill="var(--rk-k3)" x="590" y="268">ỹ₃</text>

                        <!-- K4 -->
                        <line class="k-vector k4 stroke-thin" x1="580" y1="270" x2="620" y2="260"
                            marker-end="url(#arrow-k4)" />
                        <text class="label" fill="var(--rk-k4)" x="625" y="260">K₄</text>

                        <!-- 最终平均斜率 -->
                        <line class="avg-slope" x1="280" y1="365" x2="580" y2="300" marker-end="url(#arrow-avg)" />
                        <text class="label" fill="var(--rk-avg)" x="330" y="350">Φ = (K₁ + 2K₂ + 2K₃ + K₄)/6</text>
                        <text class="label" x="370" y="585">y<sub>n+1</sub> = y<sub>n</sub> + h·Φ</text>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 图4：RK4计算流程 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">图4 RK4方法计算流程图（单步详解）</h2>
                <p class="card-subtitle">
                    每个积分步骤需要调用4次微分方程函数 f(x,y)，通过1:2:2:1的权重系数计算平均斜率Φ，然后更新(x,y)坐标。
                </p>
            </div>
            <div class="card-content">
                <div class="svg-container">
                    <svg viewBox="0 0 1200 800">
                        <!-- 流程框 -->
                        <rect class="flowchart-box" x="70" y="80" width="280" height="90" rx="15" />
                        <text class="label flowchart-title" x="90" y="115">输入初始值</text>
                        <text class="label-muted flowchart-text" x="90" y="135">(x<sub>n</sub>, y<sub>n</sub>), 步长
                            h</text>

                        <rect class="flowchart-box" x="450" y="60" width="320" height="130" rx="15" />
                        <text class="label flowchart-title" x="470" y="105">第一步：K₁ = f(x<sub>n</sub>,
                            y<sub>n</sub>)</text>
                        <text class="label-muted flowchart-text" x="470" y="135">计算起点处斜率</text>
                        <text class="label-muted flowchart-text" x="470" y="155">ỹ₁ = y<sub>n</sub> + (h/2)K₁</text>

                        <rect class="flowchart-box" x="450" y="230" width="320" height="130" rx="15" />
                        <text class="label flowchart-title" x="470" y="275">第二步：K₂ = f(x<sub>n</sub> + h/2, ỹ₁)</text>
                        <text class="label-muted flowchart-text" x="470" y="305">计算中点处斜率（使用K₁预测值）</text>
                        <text class="label-muted flowchart-text" x="470" y="325">ỹ₂ = y<sub>n</sub> + (h/2)K₂</text>

                        <rect class="flowchart-box" x="450" y="400" width="320" height="130" rx="15" />
                        <text class="label flowchart-title" x="470" y="445">第三步：K₃ = f(x<sub>n</sub> + h/2, ỹ₂)</text>
                        <text class="label-muted flowchart-text" x="470" y="475">再次计算中点处斜率（使用K₂预测值）</text>
                        <text class="label-muted flowchart-text" x="470" y="495">ỹ₃ = y<sub>n</sub> + h·K₃</text>

                        <rect class="flowchart-box" x="450" y="570" width="320" height="130" rx="15" />
                        <text class="label flowchart-title" x="470" y="615">第四步：K₄ = f(x<sub>n</sub> + h, ỹ₃)</text>
                        <text class="label-muted flowchart-text" x="470" y="645">计算终点处斜率（使用K₃预测值）</text>

                        <rect class="flowchart-box" x="840" y="250" width="320" height="200" rx="15" />
                        <text class="label flowchart-title" x="860" y="295">加权平均与更新</text>
                        <text class="label-muted flowchart-text" x="860" y="325">Φ = (K₁ + 2K₂ + 2K₃ + K₄)/6</text>
                        <text class="label-muted flowchart-text" x="860" y="355">y<sub>n+1</sub> = y<sub>n</sub> +
                            h·Φ</text>
                        <text class="label-muted flowchart-text" x="860" y="385">x<sub>n+1</sub> = x<sub>n</sub> +
                            h</text>

                        <rect class="flowchart-box" x="840" y="570" width="320" height="130" rx="15" />
                        <text class="label flowchart-title" x="860" y="615">判断是否继续</text>
                        <text class="label-muted flowchart-text" x="860" y="645">如果 x &lt; x<sub>end</sub> →
                            重复上述步骤</text>

                        <!-- 箭头连接 -->
                        <line class="flowchart-arrow" x1="350" y1="125" x2="450" y2="125" />
                        <line class="flowchart-arrow" x1="610" y1="190" x2="610" y2="230" />
                        <line class="flowchart-arrow" x1="610" y1="360" x2="610" y2="400" />
                        <line class="flowchart-arrow" x1="610" y1="530" x2="610" y2="570" />

                        <line class="flowchart-arrow" x1="770" y1="125" x2="840" y2="280" />
                        <line class="flowchart-arrow" x1="770" y1="295" x2="840" y2="300" />
                        <line class="flowchart-arrow" x1="770" y1="465" x2="840" y2="320" />
                        <line class="flowchart-arrow" x1="770" y1="635" x2="840" y2="340" />

                        <line class="flowchart-arrow" x1="1000" y1="450" x2="1000" y2="570" />

                        <!-- 反馈循环箭头 -->
                        <path class="flowchart-arrow" d="M 840 635 L 350 635 L 350 125" fill="none"
                            stroke="var(--text-secondary)" stroke-width="2.5" marker-end="url(#arrow-dark)" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card">
            <div class="card-header">
                <h2 class="card-title">📖 使用指南与技术说明</h2>
                <p class="card-subtitle">
                    高质量矢量图形，支持多种格式导出和二次编辑，适用于学术演示和教学材料制作。
                </p>
            </div>
            <div class="card-content">
                <div
                    style="display: grid; gap: var(--spacing-md); grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));">
                    <div>
                        <h3 style="color: var(--primary); margin: 0 0 var(--spacing-sm);">🎯 导出与使用</h3>
                        <ul style="margin: 0; padding-left: var(--spacing-lg); color: var(--text-secondary);">
                            <li>右键另存为 SVG 格式，可直接插入 PowerPoint 保持矢量清晰</li>
                            <li>在 PowerPoint 中可通过"设置形状格式"调整颜色、线宽和文字</li>
                            <li>需要 PNG 格式时，建议先导入 PowerPoint 再另存为图片</li>
                        </ul>
                    </div>
                    <div>
                        <h3 style="color: var(--primary); margin: 0 0 var(--spacing-sm);">⚡ 技术特性</h3>
                        <ul style="margin: 0; padding-left: var(--spacing-lg); color: var(--text-secondary);">
                            <li>响应式设计，适配多种屏幕尺寸</li>
                            <li>现代化配色方案，支持深浅主题</li>
                            <li>高质量数学符号和公式渲染</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

    </div>
</body>

</html>