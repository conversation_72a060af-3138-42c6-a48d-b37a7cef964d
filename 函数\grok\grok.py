"""
title: Grok Pipe (xAI Chat Completions) with Foldable Reasoning
author_url: https://x.ai/
author: Assistant
version: 1.0.0
license: MIT
description: OpenWebUI pipe for xAI Grok chat completions. Supports foldable reasoning (可折叠思考内容) for Grok 3 / Grok 3 Mini.
"""

from __future__ import annotations

import asyncio
import json
import random
import re
from typing import Any, AsyncGenerator, Awaitable, Callable, Dict, List, Optional, Union
import inspect

import httpx
from pydantic import BaseModel, Field


class Pipe:
    class Valves(BaseModel):
        XAI_API_KEYS: str = Field(
            default="",
            description="xAI API Keys，使用逗号分隔多个 Key（例如 xai-xxx,xai-yyy）",
        )
        BASE_URL: str = Field(
            default="https://api.x.ai/v1",
            description="xAI API 基础地址（OpenAI 兼容 Chat Completions）",
        )
        DISPLAY_MODELS: str = Field(
            default="grok-3,grok-3-mini-beta,grok-3-fast-beta,grok-2-latest",
            description="OpenWebUI 中展示的模型，逗号分隔",
        )
        DEFAULT_MAX_TOKENS: int = Field(
            default=2048, description="默认最大生成 tokens"
        )
        REQUEST_TIMEOUT: int = Field(
            default=120, description="HTTP 请求超时时间（秒）"
        )
        RETRY_ATTEMPTS: int = Field(default=3, description="失败重试次数")

        # 思考折叠相关
        SHOW_REASONING: bool = Field(
            default=True, description="是否显示思考内容（reasoning_content）"
        )
        REASONING_FORMAT: str = Field(
            default="separate", description="思考显示格式：inline 或 separate"
        )
        STREAM_REASONING: bool = Field(
            default=True, description="流式时是否按顺序输出思考片段（开启后思考将以可折叠块实时输出）"
        )
        REASONING_EFFORT: Optional[str] = Field(
            default=None,
            description="对于 Grok 3 Mini 可选：low/medium/high。未设置则不发送该字段",
        )
        PROXY_URL: Optional[str] = Field(
            default=None,
            description="HTTP 代理地址（例如 http://127.0.0.1:7890），为空则不显式使用代理",
        )
        USE_ENV_PROXY: bool = Field(
            default=True,
            description="是否读取系统环境代理（HTTP_PROXY/HTTPS_PROXY/ALL_PROXY）",
        )
        INJECT_THINKING_PROMPT: bool = Field(
            default=True,
            description="当模型未返回 reasoning_content 时，注入提示词要求用 <thinking></thinking> 包裹推理",
        )
        THINKING_TAG: str = Field(
            default="<thinking>", description="思考包裹标签的起始标记（结束将自动替换为 </thinking>）"
        )

    def __init__(self) -> None:
        self.type = "manifold"
        self.name = "Grok: "
        self.valves = self.Valves()
        self.emitter: Optional[Callable[[dict], Awaitable[None]]] = None

    # ——————————————————————————————————————————
    # OpenWebUI required hooks
    # ——————————————————————————————————————————
    def pipes(self) -> List[dict]:
        try:
            models = [m.strip() for m in self.valves.DISPLAY_MODELS.split(",") if m.strip()]
            if not models:
                return [{"id": "no-models", "name": "请在 DISPLAY_MODELS 中配置模型"}]
            return [{"id": model_id, "name": model_id} for model_id in models]
        except Exception as e:
            return [{"id": "error", "name": f"错误: 无法获取模型列表 - {str(e)}"}]

    async def pipe(
        self,
        body: Dict[str, Any],
        __event_emitter__: Callable[[dict], Awaitable[None]] | None = None,
    ) -> AsyncGenerator[str, None]:
        self.emitter = __event_emitter__

        api_key = self._pick_api_key()
        if not api_key:
            yield "Error: XAI_API_KEYS 未配置"
            return

        try:
            model_id = body.get("model", "grok-3")
            if "." in model_id:
                model_id = model_id.split(".", 1)[1]

            messages = [self._convert_message(m) for m in body.get("messages", [])]

            # 可选：注入思考提示，指导在内容中使用 <thinking> 标签
            if self.valves.SHOW_REASONING and self.valves.INJECT_THINKING_PROMPT:
                messages = self._inject_thinking_prompt(messages)

            payload: Dict[str, Any] = {
                "model": model_id,
                "messages": messages,
                "temperature": body.get("temperature", 0.7),
                "top_p": body.get("top_p", 1.0),
                "max_tokens": body.get("max_tokens", self.valves.DEFAULT_MAX_TOKENS),
                "stream": body.get("stream", True),
            }

            # 仅当配置了 REASONING_EFFORT 时才发送（主要用于 grok-3-mini 系列）
            if self.valves.REASONING_EFFORT:
                payload["reasoning_effort"] = self.valves.REASONING_EFFORT

            if "stop" in body:
                payload["stop"] = body["stop"]

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
            }

            url = self._join_url(self.valves.BASE_URL, "/chat/completions")

            # 状态提示
            if self.valves.SHOW_REASONING:
                await self._emit_status("🧠 正在思考…")
            else:
                await self._emit_status("✨ 正在生成…")

            if payload["stream"]:
                async with self._create_client(timeout=self.valves.REQUEST_TIMEOUT) as client:
                    try:
                        async with client.stream("POST", url, json=payload, headers=headers) as resp:
                            resp.raise_for_status()
                            async for chunk in self._handle_stream(resp):
                                yield chunk
                    except httpx.HTTPStatusError as e:
                        # 若 400，尝试去除 reasoning_effort 再重试（部分模型不支持）
                        if e.response is not None and e.response.status_code == 400 and "reasoning_effort" in payload:
                            payload.pop("reasoning_effort", None)
                            async with client.stream("POST", url, json=payload, headers=headers) as resp2:
                                resp2.raise_for_status()
                                async for chunk in self._handle_stream(resp2):
                                    yield chunk
                        else:
                            raise
                await self._emit_status("🎉 生成完成", done=True)
                return

            # 非流式
            try:
                data = await self._post_json(url, headers, payload)
            except httpx.HTTPStatusError as e:
                # 若 400，尝试去除 reasoning_effort 再重试
                if e.response is not None and e.response.status_code == 400 and "reasoning_effort" in payload:
                    payload.pop("reasoning_effort", None)
                    data = await self._post_json(url, headers, payload)
                else:
                    raise
            text = self._format_from_nonstreaming(data)
            await self._emit_status("🎉 生成完成", done=True)
            yield text

        except httpx.HTTPStatusError as e:
            code = getattr(e.response, "status_code", "")
            msg = f"API 请求失败: HTTP {code}"
            # 尽量包含服务端返回的错误文本，便于排查
            try:
                if e.response is not None:
                    try:
                        detail_json = e.response.json()
                        if isinstance(detail_json, dict):
                            # 常见格式 {"error": {"message": "..."}} 或 {"message": "..."}
                            if detail_json.get("error") and isinstance(detail_json["error"], dict):
                                msg += f" - {detail_json['error'].get('message', '')}"
                            elif detail_json.get("message"):
                                msg += f" - {detail_json.get('message', '')}"
                        else:
                            # 如果不是对象，退回到原始文本
                            text_snippet = e.response.text[:400]
                            if text_snippet:
                                msg += f" - {text_snippet}"
                    except Exception:
                        text_snippet = e.response.text[:400]
                        if text_snippet:
                            msg += f" - {text_snippet}"
            except Exception:
                pass
            await self._emit_status("❌ 请求失败", done=True)
            yield f"❌ {msg}"
        except Exception as e:
            await self._emit_status("❌ 发生错误", done=True)
            yield f"❌ 未知错误: {str(e)}"

    # ——————————————————————————————————————————
    # HTTP helpers
    # ——————————————————————————————————————————
    async def _post_json(self, url: str, headers: Dict[str, str], payload: Dict[str, Any]) -> Dict[str, Any]:
        last_exc: Exception | None = None
        for attempt in range(max(1, self.valves.RETRY_ATTEMPTS)):
            try:
                async with self._create_client(timeout=self.valves.REQUEST_TIMEOUT) as client:
                    resp = await client.post(url, headers=headers, json=payload)
                    resp.raise_for_status()
                    return resp.json()
            except Exception as e:
                last_exc = e
                if attempt < self.valves.RETRY_ATTEMPTS - 1:
                    await asyncio.sleep(2 ** attempt)
                else:
                    raise e
        # 逻辑兜底（理论不会到达）
        raise last_exc or RuntimeError("Unknown HTTP error")

    async def _handle_stream(self, response: httpx.Response) -> AsyncGenerator[str, None]:
        reasoning_buf: List[str] = []
        answer_started = False
        reasoning_open = False  # 是否已开始输出思考折叠块
        reasoning_closed = False  # 是否已关闭思考折叠块
        # 在流模式下，支持按顺序输出：先思考（折叠块实时展开内容），再答案
        async for raw in response.aiter_lines():
            if not raw:
                continue
            if not raw.startswith("data: "):
                continue
            payload = raw[6:].strip()
            if payload == "[DONE]":
                # 结束：若思考折叠块已打开但尚未关闭，则在末尾补上关闭标签
                if self.valves.SHOW_REASONING and self.valves.STREAM_REASONING:
                    if reasoning_open and not reasoning_closed:
                        yield "\n\n</details>\n\n---\n\n"
                        reasoning_closed = True
                else:
                    # 未启用顺序流式思考：若缓存有思考则一次性输出折叠块
                    if self.valves.SHOW_REASONING and reasoning_buf:
                        block = self._format_reasoning_block("".join(reasoning_buf))
                        yield ("\n\n" if answer_started else "") + block
                break
            try:
                data = json.loads(payload)
            except Exception:
                continue

            try:
                choice = (data.get("choices") or [{}])[0]
            except Exception:
                choice = {}

            delta = choice.get("delta") or {}

            # reasoning_content（Grok 3 Mini / 兼容字段）
            r_delta = delta.get("reasoning_content") or delta.get("reasoning")
            if isinstance(r_delta, str) and r_delta:
                reasoning_buf.append(r_delta)
                if self.valves.SHOW_REASONING and self.valves.STREAM_REASONING:
                    if not reasoning_open:
                        # 打开折叠块并实时输出思考内容
                        yield (
                            '<details type="grok.reasoning">\n'
                            '<summary>🧠 正在思考…</summary>\n\n'
                        )
                        reasoning_open = True
                    yield r_delta

            # 普通内容（可能含有 <thinking> 标签，需要按片段拆分）
            c_delta = delta.get("content")
            if isinstance(c_delta, str) and c_delta:
                if self.valves.SHOW_REASONING and self.valves.STREAM_REASONING:
                    start_tag = self.valves.THINKING_TAG
                    end_tag = start_tag.replace("<", "</")

                    i = 0
                    n = len(c_delta)
                    while i < n:
                        # 找到下一个标记
                        next_start = c_delta.find(start_tag, i)
                        next_end = c_delta.find(end_tag, i)

                        # 没有任何标记，整段输出（依据已开启状态决定输出到哪）
                        if next_start == -1 and next_end == -1:
                            chunk = c_delta[i:]
                            if reasoning_open and not reasoning_closed:
                                yield chunk
                            else:
                                # 首次输出答案前如思考未关闭，先关闭
                                if reasoning_open and not reasoning_closed:
                                    yield "\n\n</details>\n\n---\n\n"
                                    reasoning_closed = True
                                answer_started = True
                                yield chunk
                            break

                        # 下一个事件是开始标签
                        if next_start != -1 and (next_end == -1 or next_start < next_end):
                            # 输出标签前的普通文本
                            if next_start > i:
                                chunk = c_delta[i:next_start]
                                if reasoning_open and not reasoning_closed:
                                    yield chunk
                                else:
                                    if reasoning_open and not reasoning_closed:
                                        yield "\n\n</details>\n\n---\n\n"
                                        reasoning_closed = True
                                    answer_started = True
                                    yield chunk
                            # 打开思考块
                            if not reasoning_open:
                                yield (
                                    '<details type="grok.reasoning">\n'
                                    '<summary>🧠 正在思考…</summary>\n\n'
                                )
                                reasoning_open = True
                            i = next_start + len(start_tag)
                            continue

                        # 下一个事件是结束标签
                        if next_end != -1 and (next_start == -1 or next_end < next_start):
                            # 输出标签前的文本（归入思考）
                            if next_end > i:
                                yield c_delta[i:next_end]
                            # 关闭折叠块
                            if reasoning_open and not reasoning_closed:
                                yield "\n\n</details>\n\n---\n\n"
                                reasoning_closed = True
                            i = next_end + len(end_tag)
                            continue
                else:
                    answer_started = True
                    yield c_delta

    # ——————————————————————————————————————————
    # Formatting helpers
    # ——————————————————————————————————————————
    def _format_from_nonstreaming(self, data: Dict[str, Any]) -> str:
        try:
            choice = (data.get("choices") or [{}])[0]
            message = choice.get("message") or {}
            content = message.get("content", "")
            reasoning = message.get("reasoning_content", "") or message.get("reasoning", "")
        except Exception:
            content, reasoning = "", ""

        # 若未提供显式 reasoning 字段，尝试从文本标签中提取
        if self.valves.SHOW_REASONING and not reasoning and isinstance(content, str) and content:
            t, a = self._extract_tagged_reasoning(content)
            if t or a:
                reasoning, content = t, a

        if self.valves.SHOW_REASONING and reasoning:
            if (self.valves.REASONING_FORMAT or "separate").lower() == "inline":
                return self._format_inline(reasoning, content)
            return self._format_separate(reasoning, content)
        return content or ""

    def _format_inline(self, reasoning: str, answer: str) -> str:
        return (
            "💭 思考过程：\n"
            + reasoning.strip()
            + "\n\n---\n\n"
            + (answer or "")
        )

    def _format_separate(self, reasoning: str, answer: str) -> str:
        return f"{self._format_reasoning_block(reasoning)}\n\n---\n\n{(answer or '').strip()}"

    def _format_reasoning_block(self, reasoning: str) -> str:
        cleaned = reasoning.strip()
        if not cleaned:
            return ""
        # 使用 HTML 折叠块
        return (
            "<details type=\"grok.reasoning\" done=\"true\">\n"
            "<summary>🧠 查看思考过程</summary>\n\n"
            f"{cleaned}\n\n"
            "</details>"
        )

    def _extract_tagged_reasoning(self, text: str) -> tuple[str, str]:
        start_tag = self.valves.THINKING_TAG
        end_tag = start_tag.replace("<", "</")
        try:
            # 提取所有 <thinking>...</thinking>
            pattern = re.escape(start_tag) + r"([\s\S]*?)" + re.escape(end_tag)
            parts = re.findall(pattern, text)
            reasoning = "\n\n".join([p.strip() for p in parts if p.strip()])
            answer = re.sub(pattern, "", text).strip()
            return reasoning, answer
        except Exception:
            return "", text

    # ——————————————————————————————————————————
    # Misc helpers
    # ——————————————————————————————————————————
    def _convert_message(self, message: Dict[str, Any]) -> Dict[str, Any]:
        role = message.get("role", "user")
        content = message.get("content")
        if isinstance(content, str):
            return {"role": role, "content": content}
        if isinstance(content, list):
            # 兼容多模态（与 OpenAI Chat Completions 一致）
            parts: List[Dict[str, Any]] = []
            for item in content:
                if not isinstance(item, dict):
                    continue
                t = item.get("type")
                if t == "text":
                    parts.append({"type": "text", "text": item.get("text", "")})
                elif t == "image_url":
                    parts.append({"type": "image_url", "image_url": item.get("image_url", {})})
                else:
                    # 透传未知块，避免丢信息
                    parts.append(item)
            return {"role": role, "content": parts}
        # 兜底：强转为字符串
        return {"role": role, "content": str(content or "")}

    def _pick_api_key(self) -> str:
        keys = [k.strip() for k in (self.valves.XAI_API_KEYS or "").split(",") if k.strip()]
        return random.choice(keys) if keys else ""

    def _join_url(self, base: str, path: str) -> str:
        base = (base or "").rstrip("/")
        path = (path or "").lstrip("/")
        return f"{base}/{path}"

    async def _emit_status(self, message: str, *, done: bool = False) -> None:
        if not self.emitter:
            return
        await self.emitter({
            "type": "status",
            "data": {"description": message, "done": done},
        })

    def _create_client(self, *, timeout: int) -> httpx.AsyncClient:
        """创建 AsyncClient，最大化兼容不同 httpx 版本的代理参数签名。
        优先使用 AsyncClient(proxies=...)；若不支持则退回 AsyncHTTPTransport(proxy=...).
        若两者都失败，则仅依赖环境变量代理（trust_env）。
        """
        try:
            params = inspect.signature(httpx.AsyncClient.__init__).parameters
        except Exception:
            params = {}

        # 方案 A：新版本 httpx，支持 proxies
        if "proxies" in params:
            return httpx.AsyncClient(
                timeout=timeout,
                proxies=self.valves.PROXY_URL or None,
                trust_env=self.valves.USE_ENV_PROXY,
            )

        # 方案 B：老版本 httpx，使用 transport(proxy=...)
        if self.valves.PROXY_URL:
            try:
                transport = httpx.AsyncHTTPTransport(proxy=self.valves.PROXY_URL)
                return httpx.AsyncClient(
                    timeout=timeout,
                    transport=transport,
                    trust_env=self.valves.USE_ENV_PROXY,
                )
            except Exception:
                pass

        # 方案 C：仅使用环境代理
        return httpx.AsyncClient(timeout=timeout, trust_env=self.valves.USE_ENV_PROXY)

    def _inject_thinking_prompt(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        # 在现有 system 之前加一条或合并
        start_tag = self.valves.THINKING_TAG
        end_tag = start_tag.replace("<", "</")
        instruction = (
            "你是一个具有清晰推理能力的助手。请先进行必要的推理再给出答案。\n"
            f"将推理过程使用 {start_tag} 与 {end_tag} 包裹，最终答案写在标签之外。"
        )
        # 已有 system 则合并
        for m in messages:
            if m.get("role") == "system" and isinstance(m.get("content"), str):
                m["content"] = (m.get("content") or "") + "\n\n" + instruction
                return messages
        return [{"role": "system", "content": instruction}] + messages


