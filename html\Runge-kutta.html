<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>RK4 四阶龙格-库塔法 - SVG 示意图</title>
    <style>
        :root {
            --axis: #6b7280;
            --grid: #e5e7eb;
            --k1: #4e79a7;
            --k2: #f28e2b;
            --k3: #59a14f;
            --k4: #e15759;
            --step: #9c5cc0;
            --text: #111827;
        }

        html,
        body {
            margin: 0;
            padding: 0;
            font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto,
                Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
            color: var(--text);
            background: #ffffff;
        }

        .page {
            max-width: 1100px;
            margin: 24px auto;
            padding: 0 16px 40px 16px;
        }

        h1 {
            font-size: 22px;
            margin: 4px 0 12px;
        }

        p.desc {
            margin: 0 0 14px 0;
            color: #374151;
        }

        .container {
            display: grid;
            grid-template-columns: 1fr 330px;
            gap: 16px;
            align-items: start;
        }

        .panel {
            border: 1px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 14px;
            background: #fafafa;
        }

        code,
        .mono {
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
                "Liberation Mono", "Courier New", monospace;
        }

        .legend {
            display: grid;
            grid-template-columns: 24px 1fr;
            row-gap: 8px;
            column-gap: 8px;
            margin-top: 8px;
        }

        .key {
            height: 12px;
            border-radius: 6px;
        }

        .formula {
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-line;
        }

        .muted {
            color: #6b7280;
        }

        svg text {
            user-select: none;
        }
    </style>
</head>

<body>
    <div class="page">
        <h1>四阶经典龙格-库塔法（RK4）— SVG 过程示意</h1>
        <p class="desc">
            该图以一个简单的一阶常微分方程 \( y' = f(x, y) = x + y \) 为例，演示在步长
            \(h\) 内从 \((x_n, y_n)\) 前进到 \((x_{n+1}, y_{n+1})\) 的四个斜率阶段
            <span class="mono">K1, K2, K3, K4</span> 及其加权合成。图形仅为示意，便于理解各阶段的几何意义。
        </p>

        <div class="container">
            <!-- 画布区域 -->
            <div class="panel">
                <svg id="rk4-svg" width="100%" viewBox="0 0 900 520" xmlns="http://www.w3.org/2000/svg">
                    <!-- 网格 -->
                    <defs>
                        <marker id="arrow" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L10,4 L0,8 Z" fill="#6b7280" />
                        </marker>

                        <marker id="arrow-k1" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L12,5 L0,10 Z" fill="var(--k1)" />
                        </marker>
                        <marker id="arrow-k2" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L12,5 L0,10 Z" fill="var(--k2)" />
                        </marker>
                        <marker id="arrow-k3" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L12,5 L0,10 Z" fill="var(--k3)" />
                        </marker>
                        <marker id="arrow-k4" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L12,5 L0,10 Z" fill="var(--k4)" />
                        </marker>
                        <marker id="arrow-step" markerWidth="10" markerHeight="8" refX="9" refY="4" orient="auto"
                            markerUnits="userSpaceOnUse">
                            <path d="M0,0 L10,4 L0,8 Z" fill="var(--step)" />
                        </marker>
                    </defs>

                    <!-- 坐标系参数（通过 JS 计算绘制） -->
                    <g id="grid"></g>
                    <g id="axes"></g>

                    <!-- 近似解轨迹与 RK4 结果连线（仅示意） -->
                    <path id="solution" fill="none" stroke="#cbd5e1" stroke-width="2" />

                    <!-- 起点与终点 -->
                    <g id="points"></g>

                    <!-- 四个斜率箭头 -->
                    <g id="slopes"></g>

                    <!-- 文字标签层 -->
                    <g id="labels"></g>
                </svg>
            </div>

            <!-- 说明与图例 -->
            <div class="panel">
                <div class="legend">
                    <div class="key" style="background: var(--k1)"></div>
                    <div>K1 = f(x<sub>n</sub>, y<sub>n</sub>)（起点斜率）</div>
                    <div class="key" style="background: var(--k2)"></div>
                    <div>K2 = f(x<sub>n</sub>+h/2, y<sub>n</sub>+hK1/2)（前半步中点斜率）</div>
                    <div class="key" style="background: var(--k3)"></div>
                    <div>K3 = f(x<sub>n</sub>+h/2, y<sub>n</sub>+hK2/2)（后半步中点斜率）</div>
                    <div class="key" style="background: var(--k4)"></div>
                    <div>K4 = f(x<sub>n</sub>+h, y<sub>n</sub>+hK3)（终点斜率）</div>
                    <div class="key" style="background: var(--step)"></div>
                    <div>从 (x<sub>n</sub>, y<sub>n</sub>) 到 (x<sub>n</sub>+h, y<sub>n+1</sub>) 的一步</div>
                </div>

                <hr style="border:none;border-top:1px solid #e5e7eb;margin:12px 0" />

                <div class="formula">
                    <div><b>加权更新：</b></div>
                    <div>y<sub>n+1</sub> = y<sub>n</sub> + (h/6) · (K1 + 2·K2 + 2·K3 + K4)</div>
                    <div class="muted" style="margin-top:8px;">示例选择：f(x, y) = x + y，x<sub>n</sub> = 0，y<sub>n</sub> =
                        0.5，h = 1</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 该示例用 y' = f(x, y) = x + y 演示 RK4 四个阶段
        (function () {
            /** 坐标与比例设置 */
            const width = 900;
            const height = 520;
            const margin = { left: 60, right: 20, top: 20, bottom: 56 };
            const origin = { x: margin.left, y: height - margin.bottom };
            const innerWidth = width - margin.left - margin.right;
            const innerHeight = height - margin.top - margin.bottom;

            // 数学坐标到屏幕坐标的线性变换
            const units = { x: 1.0, y: 1.0 }; // 逻辑尺寸（以单位为 1 的步长方便演示）
            const xScale = innerWidth / 8;   // 8 个单位宽度
            const yScale = innerHeight / 6;  // 6 个单位高度

            const toPx = (x, y) => [origin.x + x * xScale, origin.y - y * yScale];

            /** ODE 与示例参数 */
            const f = (x, y) => x + y; // 简单线性：便于观察斜率走势
            const xn = 0.0;
            const yn = 0.5;
            const h = 1.0;

            // 四个斜率阶段
            const K1 = f(xn, yn);
            const K2 = f(xn + h / 2, yn + (h / 2) * K1);
            const K3 = f(xn + h / 2, yn + (h / 2) * K2);
            const K4 = f(xn + h, yn + h * K3);
            const yn1 = yn + (h / 6) * (K1 + 2 * K2 + 2 * K3 + K4);

            // 用短水平增量可视化斜率箭头（仅示意）
            const arrowDX = 0.8; // 以数学单位表示的水平长度
            const slopeSegment = (x0, y0, slope) => {
                const x1 = x0 + arrowDX;
                const y1 = y0 + slope * arrowDX; // dy = slope * dx
                return { x1, y1 };
            };

            // 各阶段的参考点（数学坐标）
            const P1 = { x: xn, y: yn };
            const P2 = { x: xn + h / 2, y: yn + (h / 2) * K1 };
            const P3 = { x: xn + h / 2, y: yn + (h / 2) * K2 };
            const P4 = { x: xn + h, y: yn + h * K3 };
            const Pend = { x: xn + h, y: yn1 };

            // 绘制函数：用平滑样条近似一条解曲线，仅用于视觉参考
            const solutionPath = (() => {
                const samples = 80;
                const path = [];
                let x = xn - 0.4; // 从起点稍微向左绘制
                let y = yn - 0.3; // 选个接近的初值，轨迹仅示意
                const dt = (h * 1.6) / samples; // 跨过 1.6h 的可视范围
                for (let i = 0; i <= samples; i++) {
                    const [px, py] = toPx(x, y);
                    path.push(`${i === 0 ? 'M' : 'L'}${px},${py}`);
                    // 用欧拉细步积分粗略生成一条“解”的参考轨迹
                    const dy = f(x, y) * dt;
                    x += dt;
                    y += dy;
                }
                return path.join(' ');
            })();

            // 构建网格与坐标轴
            const svg = document.getElementById('rk4-svg');
            const grid = document.getElementById('grid');
            const axes = document.getElementById('axes');
            const labels = document.getElementById('labels');
            const slopes = document.getElementById('slopes');
            const points = document.getElementById('points');

            // 网格线
            const drawGrid = () => {
                const elems = [];
                for (let i = 0; i <= 8; i++) {
                    const x = origin.x + i * xScale;
                    elems.push(`<line x1="${x}" y1="${margin.top}" x2="${x}" y2="${height - margin.bottom}" stroke="var(--grid)" stroke-width="1" />`);
                }
                for (let j = 0; j <= 6; j++) {
                    const y = origin.y - j * yScale;
                    elems.push(`<line x1="${margin.left}" y1="${y}" x2="${width - margin.right}" y2="${y}" stroke="var(--grid)" stroke-width="1" />`);
                }
                grid.innerHTML = elems.join('');
            };

            // 坐标轴与刻度
            const drawAxes = () => {
                const [x0, y0] = [margin.left, origin.y];
                const [x1, y1] = [width - margin.right, origin.y];
                const [x2, y2] = [origin.x, margin.top];
                axes.innerHTML = `
            <line x1="${x0}" y1="${y0}" x2="${x1}" y2="${y1}" stroke="var(--axis)" stroke-width="2" marker-end="url(#arrow)" />
            <line x1="${origin.x}" y1="${height - margin.bottom}" x2="${origin.x}" y2="${y2}" stroke="var(--axis)" stroke-width="2" marker-end="url(#arrow)" />
            <text x="${x1 - 12}" y="${y1 - 8}" font-size="12">x</text>
            <text x="${origin.x + 8}" y="${margin.top + 16}" font-size="12">y</text>
          `;
            };

            // 斜率箭头
            const drawSlopeArrow = (pt, k, color, id) => {
                const seg = slopeSegment(pt.x, pt.y, k);
                const [xA, yA] = toPx(pt.x, pt.y);
                const [xB, yB] = toPx(seg.x1, seg.y1);
                return `<line id="${id}" x1="${xA}" y1="${yA}" x2="${xB}" y2="${yB}" stroke="${color}" stroke-width="3" marker-end="url(#arrow-${id})" />`;
            };

            // 统一设置不同颜色的 marker id
            const idMap = { k1: 'k1', k2: 'k2', k3: 'k3', k4: 'k4' };

            const draw = () => {
                drawGrid();
                drawAxes();

                // 参考“解”曲线
                document.getElementById('solution').setAttribute('d', solutionPath);

                // 起点、四个阶段参考点、终点
                const pointElems = [];
                const labelElems = [];
                const pointsData = [
                    { p: P1, text: 'P₁: (xₙ, yₙ)', dy: -10 },
                    { p: P2, text: 'P₂: (xₙ+h/2, yₙ+hK₁/2)', dy: -10 },
                    { p: P3, text: 'P₃: (xₙ+h/2, yₙ+hK₂/2)', dy: -10 },
                    { p: P4, text: 'P₄: (xₙ+h, yₙ+hK₃)', dy: -10 },
                    { p: Pend, text: 'P₅: (xₙ+h, yₙ₊₁)', dy: -10 },
                ];

                pointsData.forEach(({ p, text, dy }, i) => {
                    const [cx, cy] = toPx(p.x, p.y);
                    pointElems.push(`<circle cx="${cx}" cy="${cy}" r="4" fill="#111827"/>`);
                    labelElems.push(`<text x="${cx + 6}" y="${cy + dy}" font-size="12" fill="#111827">${text}</text>`);
                });
                points.innerHTML = pointElems.join('');

                // 斜率箭头
                const slopeElems = [];
                slopeElems.push(drawSlopeArrow(P1, K1, 'var(--k1)', idMap.k1));
                slopeElems.push(drawSlopeArrow(P2, K2, 'var(--k2)', idMap.k2));
                slopeElems.push(drawSlopeArrow(P3, K3, 'var(--k3)', idMap.k3));
                slopeElems.push(drawSlopeArrow(P4, K4, 'var(--k4)', idMap.k4));

                // 从 (x_n, y_n) 到 (x_n+h, y_{n+1}) 的整体一步
                const [sx0, sy0] = toPx(P1.x, P1.y);
                const [sx1, sy1] = toPx(Pend.x, Pend.y);
                slopeElems.push(`<line x1="${sx0}" y1="${sy0}" x2="${sx1}" y2="${sy1}" stroke="var(--step)" stroke-width="3" marker-end="url(#arrow-step)" />`);
                slopes.innerHTML = slopeElems.join('');

                // 标签与数值
                const fmt = (v) => (Math.abs(v) < 1e-6 ? '0' : v.toFixed(3));
                labelElems.push(
                    `<text x="${sx1 + 8}" y="${sy1 - 6}" font-size="12" fill="var(--step)">yₙ₊₁ = ${fmt(yn1)}</text>`
                );
                labelElems.push(`<text x="${sx0 + 10}" y="${sy0 + 18}" font-size="12" fill="var(--k1)">K1=${fmt(K1)}</text>`);
                const [t2x, t2y] = toPx(P2.x, P2.y);
                labelElems.push(`<text x="${t2x + 10}" y="${t2y + 18}" font-size="12" fill="var(--k2)">K2=${fmt(K2)}</text>`);
                const [t3x, t3y] = toPx(P3.x, P3.y);
                labelElems.push(`<text x="${t3x + 10}" y="${t3y + 18}" font-size="12" fill="var(--k3)">K3=${fmt(K3)}</text>`);
                const [t4x, t4y] = toPx(P4.x, P4.y);
                labelElems.push(`<text x="${t4x + 10}" y="${t4y + 18}" font-size="12" fill="var(--k4)">K4=${fmt(K4)}</text>`);
                labels.innerHTML = labelElems.join('');
            };

            draw();
        })();
    </script>
</body>

</html>