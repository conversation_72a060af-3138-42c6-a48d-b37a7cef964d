# ------------- Standard & Third-Party Imports -------------
import time
import argparse
import os
from dotenv import load_dotenv
import google.generativeai as genai
from google.generativeai import types

load_dotenv()

parser = argparse.ArgumentParser(description="Generate video using Google Gemini VEO model")
parser.add_argument("--prompt", type=str, default="展现高密度等离子体中高能电子激发或内层电子空穴复合，离子内部能级跃迁导致特定能量的光子射出，屏幕上出现明亮清晰的光谱线（像色散后的彩虹条）")
parser.add_argument("--model", type=str, default="veo-3.0-generate-preview")
parser.add_argument("--duration", type=int, default=5)
parser.add_argument("--aspect_ratio", type=str, default="16:9")
parser.add_argument("--output", type=str, default="output.mp4")
parser.add_argument("--api_key", type=str, default='AIzaSyBTG-niTa5GZ9KFN602F4zQovPyDxl2xYY')
parser.add_argument("--interval", type=int, default=10)
args = parser.parse_args()

API_KEY = args.api_key or os.getenv("GOOGLE_API_KEY")
if not API_KEY:
    raise SystemExit("GOOGLE_API_KEY not set. Provide via --api_key or .env file")

client = genai.Client(api_key=API_KEY)

prompt = args.prompt

operation = client.models.generate_videos(
    model=args.model,
    prompt=prompt,
    parameters={"durationSeconds": args.duration, "aspectRatio": args.aspect_ratio},
)

while not operation.done:
    print("Waiting for video generation to complete...")
    time.sleep(args.interval)
    operation = client.operations.get(operation)

generated_video = operation.response.generated_videos[0]
client.files.download(file=generated_video.video)
output_path = os.path.abspath(args.output)
generated_video.video.save(output_path)
print(f"Generated video saved to {output_path}")